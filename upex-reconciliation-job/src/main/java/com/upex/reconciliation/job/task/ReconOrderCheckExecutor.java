package com.upex.reconciliation.job.task;

import com.upex.config.account.SystemAccountQueryByTypeListReq;
import com.upex.config.account.SystemAccountQueryReq;
import com.upex.config.account.enums.SystemAccountTypeEnum;
import com.upex.config.facade.account.SystemAccountService;
import com.upex.reconciliation.service.business.convert.OrderDataMatcher;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.task.DelayOrderScanningTask;
import com.upex.reconciliation.service.business.convert.task.DelayReconOrderTask;
import com.upex.reconciliation.service.consumer.kafka.ConvertOrderConsumerRunnable;
import com.upex.reconciliation.service.consumer.kafka.ConvertSpotConsumerRunnable;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.redisson.api.RMapCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 订单对账检查执行器
 * 该类用于执行订单对账检查任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReconOrderCheckExecutor {


    @Resource
    private OrderDataMatcher orderDataMatcher;
    @Resource
    private SystemAccountService systemAccountService;
    @Resource
    private DelayOrderScanningTask delayOrderScanningTask;

    /**
     * 执行闪兑订单对账检查
     */
    @XxlJob("reconOrderCheckJob")
    public void executeCheck() {

        log.info("ReconOrderCheckJob started");
        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        KafkaConsumerConfig orderKafkaConsumerConfig = reconOrderConfig.getOrderKafkaConsumerConfig();

        ConvertOrderConsumerRunnable convertOrderConsumerRunnable = new ConvertOrderConsumerRunnable(null, orderDataMatcher);
        new Thread(convertOrderConsumerRunnable, convertOrderConsumerRunnable.getThreadPrefixName()).start();


//            KafkaConsumerConfig kafkaConsumerConfig = new KafkaConsumerConfig();
//            kafkaConsumerConfig.setTopicName("recon_consumer_bill_type_10");
//            kafkaConsumerConfig.setAccountType("10");
//            ConvertSpotConsumerRunnable convertSpotConsumerRunnable = new ConvertSpotConsumerRunnable("dev-test-v351-kafka01.bitget.tools:9092,dev-test-v351-kafka02.bitget.tools:9092,dev-test-v351-kafka03.bitget.tools:9092",
//                    kafkaConsumerConfig, accountAssetsServiceFactory, alarmNotifyService,orderDataMatcher);
//            new Thread(convertSpotConsumerRunnable, "ConvertSpotConsumerThread").start();
    }

    /**
     * 执行订单对账超时检查
     */
    @XxlJob("delayReconOrderScanJob")
    public void delayReconOrderScanJob() {
        try {
            log.info("DelayReconOrderScanJob started");
            delayOrderScanningTask.scanDelayOrders();
        } catch (Exception e) {
            log.error("DelayReconOrderScanJob error", e);
        }

    }
}
