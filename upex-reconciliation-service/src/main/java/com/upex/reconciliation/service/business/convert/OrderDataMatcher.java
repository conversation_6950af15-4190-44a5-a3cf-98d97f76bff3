package com.upex.reconciliation.service.business.convert;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.CombinedOrderData;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.model.ConvertOrder;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;

@Component
@Slf4j
public class OrderDataMatcher {

    private static final DateTimeFormatter SHARD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private OrderDataProcessor orderDataProcessor;

    private final ReconOrderConfig reconOrderConfig;

    public OrderDataMatcher() {
        this.reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    /**
     * 保存主订单数据
     *
     * @param orderData 主订单数据
     */
    public void saveMainOrderData(ConvertOrder orderData) {
        if (orderData == null || orderData.getOrderId() == null) {
            log.warn("Invalid main order data: {}", orderData);
            return;
        }
        String orderId = String.valueOf(orderData.getOrderId());
        String key = MAIN_ORDER_PREFIX + orderId;
        String value = JSON.toJSONString(orderData);
        // 保存主订单数据到Redis
        redisTemplate.opsForValue().set(key, value, reconOrderConfig.getOrderExpireTime(), TimeUnit.MINUTES);
        // 保存订单ID到索引
        saveMainOrderIndex(orderId);
        log.info("Saved main order data: {}", orderId);
        // 对账
        tryMatchOrder(orderId);
    }

    /**
     * 保存流水订单数据
     *
     * @param bill    流水订单数据
     * @param bizType 业务类型
     */
    public void saveFlowOrderData(ConvertBill bill, String bizType) {

        if (bill == null || bill.getOrderId() == null || !reconOrderConfig.getFlowTypes().contains(bizType)) {
            log.warn("Invalid or unsupported flow order data: {}, bizType: {}", bill, bizType);
            return;
        }
        String orderId = String.valueOf(bill.getOrderId());
        String key = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
        String value = JSON.toJSONString(bill);
        // 保存流水订单数据到Redis
        redisTemplate.opsForValue().set(key, value, reconOrderConfig.getOrderExpireTime(), TimeUnit.MINUTES);
        log.info("Saved flow order data: {}, bizType: {}", orderId, bizType);
        // 保存订单ID到索引
        saveMainOrderIndex(orderId);
        // 尝试对账
        tryMatchOrder(orderId);
    }

    /**
     * 尝试匹配订单进行对账
     *
     * @param orderId 订单ID
     */
    public void tryMatchOrder(String orderId) {
        // 获取主订单数据
        String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
        Object order = redisTemplate.opsForValue().get(mainOrderKey);
        if (Objects.isNull(order) || StringUtils.isEmpty(order.toString())) {
            log.debug("Main order not found for matching: {}", orderId);
            return;
        }
        // 检查所有需要的流水订单类型是否都存在
        boolean allFlowOrdersExist = true;

        for (String bizType : reconOrderConfig.getFlowTypes()) {
            String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
            Boolean exists = redisTemplate.hasKey(flowOrderKey);
            if (Boolean.FALSE.equals(exists)) {
                allFlowOrdersExist = false;
                log.debug("Flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                break;
            }
        }
        // 如果所有需要的流水订单都存在，则进行匹配处理
        if (allFlowOrdersExist) {
            ConvertOrder mainOrderData = JSON.parseObject(order.toString(), ConvertOrder.class);
            // 创建组合订单对象
            CombinedOrderData combinedOrderData = new CombinedOrderData();
            combinedOrderData.setMainOrder(mainOrderData);

            // 获取所有流水订单数据
            for (String bizType : reconOrderConfig.getFlowTypes()) {
                String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                Object bill = redisTemplate.opsForValue().get(flowOrderKey);
                if (Objects.nonNull(bill)) {
                    ConvertBill flowOrderData = JSON.parseObject(bill.toString(), ConvertBill.class);
                    combinedOrderData.getFlowOrders().put(flowOrderKey, flowOrderData);
                }
            }
            // 处理组合订单
            ReconOrderResult orderResult = orderDataProcessor.processCombinedOrder(combinedOrderData);
            if (orderResult.isSuccess()) {
                // 删除已处理的订单数据
                redisTemplate.delete(mainOrderKey);
                // 批量删除流水订单数据
                List<String> flowOrderKeys = new ArrayList<>();
                for (String bizType : reconOrderConfig.getFlowTypes()) {
                    flowOrderKeys.add(FLOW_ORDER_PREFIX + bizType + ":" + orderId);
                }
                redisTemplate.delete(flowOrderKeys);
                if (reconOrderConfig.isDynamicShardingEnabled()) {
                    // 删除主订单索引
                    String shardKey = getDynamicShardKey();
                    redisTemplate.opsForZSet().remove(shardKey, orderId);
                } else {
                    redisTemplate.opsForZSet().remove(DEFAULT_MAIN_ORDER_INDEX_KEY, orderId);
                }
                cleanupRedisData(orderId);
                log.info("Successfully matched and processed order: {}", orderId);
            } else {
                // 对账失败，记录失败信息
                recordFailure(mainOrderData, orderResult);
                log.warn("Failed to process combined order: {}", orderId);
            }
        }
    }


    /**
     * 清理Redis中的订单数据
     */
    private void cleanupRedisData(String orderId) {
        try {
            // 删除主订单数据
            String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
            redisTemplate.delete(mainOrderKey);

            // 删除流水订单数据
            ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
            List<String> flowOrderKeys = new ArrayList<>();
            for (String bizType : reconOrderConfig.getFlowTypes()) {
                flowOrderKeys.add(FLOW_ORDER_PREFIX + bizType + ":" + orderId);
            }
            if (!flowOrderKeys.isEmpty()) {
                redisTemplate.delete(flowOrderKeys);
            }

            // 删除索引数据
            if (reconOrderConfig.isDynamicShardingEnabled()) {
                // 动态分片情况下需要计算分片key
                String shardKey = MAIN_ORDER_TIMESTAMP_PREFIX +
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
                redisTemplate.opsForZSet().remove(shardKey, orderId);
            } else {
                redisTemplate.opsForZSet().remove(DEFAULT_MAIN_ORDER_INDEX_KEY, orderId);
            }

            log.info("Cleaned up Redis data for order: {}", orderId);

        } catch (Exception e) {
            log.warn("Failed to cleanup Redis data for order: {}", orderId, e);
            // 清理失败不影响对账结果
        }
    }
    /**
     * 新增主订单索引保存的方法
     */
    private void saveMainOrderIndex(String orderId) {
        String shardKey = getDynamicShardKey();
        redisTemplate.opsForZSet().add(shardKey, orderId, System.currentTimeMillis());
        if (reconOrderConfig.isDynamicShardingEnabled()) {
            // 设置分片过期时间
            redisTemplate.expire(shardKey, reconOrderConfig.getOrderExpireTime() + 10, TimeUnit.MINUTES);
        }
        log.info("Saved main order index for shard key: {}", shardKey);
    }

    private String getDynamicShardKey() {
        if (reconOrderConfig.isDynamicShardingEnabled()) {
            LocalDateTime now = LocalDateTime.now();
            return MAIN_ORDER_TIMESTAMP_PREFIX + now.format(SHARD_FORMATTER);
        }
        return DEFAULT_MAIN_ORDER_INDEX_KEY;
    }

    /**
     * 记录失败信息
     *
     * @param mainOrderData 主订单数据
     * @param orderResult   对账结果
     */
    private void recordFailure(ConvertOrder mainOrderData, ReconOrderResult orderResult) {
        ReconOrderFailureRecord failureRecord = ReconOrderFailureRecord.builder()
                .bizId(mainOrderData.getOrderId())
                .orderId(mainOrderData.getOrderId())
                .bizType(ReconOrderTypeEnum.CONVERT.getCode())
                .failureType(orderResult.getFailureType().toString())
                .failureReason(orderResult.getFailureType().getMessage())
                .status(ReconOrderStausEnum.RECONCILED_FAILURE.getCode()) // 对账失败
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        reconOrderFailureRecordService.recordFailure(failureRecord);
    }
}
