package com.upex.reconciliation.service.model.config;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ReconOrderConfig {

    /**是否开启*/
    private boolean isOpen = true;
    /**对账系统类型*/
    private Byte orderType;
    /**开启订单分片*/
    private boolean dynamicShardingEnabled =false;
    private List<String> flowTypes=new ArrayList<>();
    private List<String> userBizTypes=new ArrayList<>();
    private List<String> systemBizTypes=new ArrayList<>();
   /**系统账号**/
    private Long systemUserId;

    /**订单过期时间，单位分钟*/
    private int orderExpireTime = 15;
    /**扫描超时订单**/
    private int scanTimeout = 15 * 60 * 1000;
    /** 重试次数 */
    private int retryCount = 5;
    /** 分片扫描范围，单位分钟 */
    private int shardScope = 30;
    /** 是否开启行情价计算*/
    private boolean enableQuotaPrice = true;
    /**利润价格和成本价低差异比率*/
    private String profitCostRate = "0.01";
    /**行情价最高阈值*/
    private String highThresholdRate = "0.1";
    /**行情价最低阈值*/
    private String lowThresholdRate = "0.1";

    /**闪兑订单消息topic*/
    private KafkaConsumerConfig orderKafkaConsumerConfig;
    /**闪兑流水消息topic*/
    private KafkaConsumerConfig flowKafkaConsumerConfig;
    /**订单过滤类型**/
    private List<String> orderFilterStatus = Lists.newArrayList("2");
    /**流水过滤类型**/
    private List<String> flowFilterTypes = new ArrayList<>();
//  "accountType": 10,
//          "topicName": "recon_consumer_bill_type_10_prod",
//          "consumerGroupId": "bg_recon-upex-reconciliation-job-recon_consumer_bill_type_10_user_profit_group",
//          "kafkaBatchSize": 500,
//          "serviceInstanceName": "upex-reconciliation-job04",
//          "isOpen": true,
//          "partitionNum": 6,
//          "msgRateLimit": 10000


}
