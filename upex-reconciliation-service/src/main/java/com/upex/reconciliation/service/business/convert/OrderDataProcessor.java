package com.upex.reconciliation.service.business.convert;

import com.upex.kline.dto.response.KLinePeriodDto;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.convert.enums.ReconException;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderFailureEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.CombinedOrderData;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.remote.KlineInnerService;
import com.upex.reconciliation.service.common.constants.enums.CoinIdEnum;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;

@Slf4j
@Component
public class OrderDataProcessor {

    @Resource
    private KlineInnerService klineInnerService;

    @Resource
    private CommonService commonService;

    @Resource
    private AlarmNotifyService alarmNotifyService;

    private final ReconOrderConfig reconOrderConfig;

    public OrderDataProcessor() {
        this.reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    /**
     * 处理组合订单数据
     * @param combinedOrderData 组合订单数据
     * @return 处理结果
     */
    public ReconOrderResult processCombinedOrder(CombinedOrderData combinedOrderData) {
        try {
            log.info("Processing combined order: {}", combinedOrderData.getOrderId());

            // 金额检查
            validAmount(combinedOrderData);
            // 成交价格检查
            validatePriceData(combinedOrderData);
            // 行情价格检查
            validQuotaPrice(combinedOrderData);

            log.info("Successfully processed combined order: {}", combinedOrderData.getOrderId());
            return ReconOrderResult.success();
        } catch (ReconException e) {
            return ReconOrderResult.failure(e.getFailureEnum(), e.getMessage());
        } catch (Exception e) {
            log.error("Error processing combined order: {}", combinedOrderData.getOrderId(), e);
            alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                    ReconOrderTypeEnum.CONVERT,
                    combinedOrderData.getOrderId(),
                    e.getMessage());
            return ReconOrderResult.failure(ReconOrderFailureEnum.UNKNOWN_ERROR, e.getMessage());
        }
    }

    /**
     * 检查价格
     * @param combinedOrderData 组合订单数据
     */
    private void validAmount(CombinedOrderData combinedOrderData) {
        // 获取配置的业务类型
        List<String> systemBizTypes = reconOrderConfig.getSystemBizTypes();
        List<String> userBizTypes = reconOrderConfig.getUserBizTypes();
        // 计算用户业务类型的余额变更总和
        BigDecimal userBalanceChange = BigDecimal.ZERO;
        for (String bizType : userBizTypes) {
            ConvertBill flowOrder = combinedOrderData.getFlowOrders().get(bizType);
            if (flowOrder != null) {
                userBalanceChange = userBalanceChange.add(flowOrder.getBalanceChange());
            }
        }
        // 计算系统业务类型的余额变更总和
        BigDecimal systemBalanceChange = BigDecimal.ZERO;
        for (String bizType : systemBizTypes) {
            ConvertBill flowOrder = combinedOrderData.getFlowOrders().get(bizType);
            if (flowOrder != null) {
                systemBalanceChange = systemBalanceChange.add(flowOrder.getBalanceChange());
            }
        }
        // 校验用户和系统的余额变更应该相互抵消（总和为0）
        BigDecimal totalBalanceChange = userBalanceChange.add(systemBalanceChange);
        if (totalBalanceChange.compareTo(BigDecimal.ZERO) != 0) {
            log.error("Balance change inconsistency detected for order: {}, userBalanceChange: {}, systemBalanceChange: {}, totalBalanceChange: {}",
                    combinedOrderData.getOrderId(), userBalanceChange, systemBalanceChange, totalBalanceChange);
            String errorMessage = String.format("余额变更不一致, 用户余额变更: %s, 系统余额变更: %s, 总变更: %s",
                    userBalanceChange, systemBalanceChange, totalBalanceChange);
            alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                    ReconOrderTypeEnum.CONVERT,
                    combinedOrderData.getOrderId(),
                    errorMessage);
            throw new ReconException(ReconOrderFailureEnum.INCONSISTENT_BALANCE_CHANGE, "余额变更不一致，订单ID: " + combinedOrderData.getOrderId());
        }
        // 校验订单金额与用户流水金额一致
        // 根据币种分别计算用户流水的余额变更
        BigDecimal userFromCoinChange = BigDecimal.ZERO;
        BigDecimal userToCoinChange = BigDecimal.ZERO;
        for (String bizType : userBizTypes) {
            ConvertBill flowOrder = combinedOrderData.getFlowOrders().get(bizType);
            if (flowOrder != null) {
                // 根据币种ID判断是from币种还是to币种的变更
                if (flowOrder.getCoinId().equals(combinedOrderData.getMainOrder().getFromCoinId())) {
                    userFromCoinChange = userFromCoinChange.add(flowOrder.getBalanceChange());
                } else if (flowOrder.getCoinId().equals(combinedOrderData.getMainOrder().getToCoinId())) {
                    userToCoinChange = userToCoinChange.add(flowOrder.getBalanceChange());
                }
            }
        }
        // 验证主订单金额与用户流水金额的一致性
        // 用户出币应该是负数，所以主订单的fromCoinCount应该等于用户流水变更的绝对值
        BigDecimal fromCoinCountDiff = combinedOrderData.getMainOrder().getFromCoinCount().add(userFromCoinChange);
        // 用户入币应该是正数，所以主订单的toCoinCount应该等于用户流水变更
        BigDecimal toCoinCountDiff = combinedOrderData.getMainOrder().getToCoinCount().subtract(userToCoinChange);
        if (fromCoinCountDiff.compareTo(BigDecimal.ZERO) != 0 || toCoinCountDiff.compareTo(BigDecimal.ZERO) != 0) {
            log.error("Coin count inconsistency detected for order: {},userFromCoinChange: {},  fromCoinCountDiff: {}, userToCoinChange: {},  toCoinCountDiff: {}",
                    combinedOrderData.getOrderId(), userFromCoinChange,fromCoinCountDiff,userToCoinChange, toCoinCountDiff);
            String errorMessage = String.format("订单金额与流水金额不一致, fromCoinCount: %s,  fromCoin差异: %s, toCoinCount: %s, toCoin差异: %s",
                    userFromCoinChange,fromCoinCountDiff, userToCoinChange,toCoinCountDiff);
            alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                    ReconOrderTypeEnum.CONVERT,
                    combinedOrderData.getOrderId(),
                    errorMessage);
            throw new ReconException(ReconOrderFailureEnum.INCONSISTENT_COIN_COUNT, "订单金额与流水金额不一致，订单ID: " + combinedOrderData.getOrderId());
        }
        log.info("Reconciliation performed successfully for order: {}, userBalanceChange: {}, systemBalanceChange: {}",
                combinedOrderData.getOrderId(), userBalanceChange, systemBalanceChange);
    }

    private void validatePriceData(CombinedOrderData combinedOrderData) {
        // 如果pt2和pc2都为null，说明是USDT交易对
        boolean isUsdt = Objects.isNull(combinedOrderData.getMainOrder().getPt2()) &&
                Objects.isNull(combinedOrderData.getMainOrder().getPc2());
        if (Objects.isNull(combinedOrderData.getMainOrder().getPt1()) || Objects.isNull(combinedOrderData.getMainOrder().getPc1())) {
            log.error("Price data is missing for order: {}", combinedOrderData.getOrderId());
            String errorMessage = "价格数据缺失, pt1或pc1为空";
            alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                    ReconOrderTypeEnum.CONVERT,
                    combinedOrderData.getOrderId(),
                    errorMessage);
            throw new ReconException(ReconOrderFailureEnum.PRICE_DATA_MISSING, "价格数据缺失，订单ID: " + combinedOrderData.getOrderId());
        }
        // 利润价格
        BigDecimal profitPrice = isUsdt ? combinedOrderData.getMainOrder().getPt1() : combinedOrderData.getMainOrder().getPt2();
        // 成本价格
        BigDecimal costPrice = isUsdt ? combinedOrderData.getMainOrder().getPc1() : combinedOrderData.getMainOrder().getPc2();
        // 检查利润价格和成本价格的差异是否超过阈值
        String profitCostRate = reconOrderConfig.getProfitCostRate();
        boolean isPriceDifferenceExceedsThreshold = profitPrice.subtract(costPrice).abs().compareTo(new BigDecimal(profitCostRate)) > 0;
        if (isUsdt) {
            // USDT交易对，判断买卖方向
            boolean isBuyDirection = combinedOrderData.getMainOrder().getFromCoinId().equals(CoinIdEnum.USDT.getCode());
            // 如果是买入方向，利润价格应该大于成本价格；如果是卖出方向，成本价格应该大于利润价格
            if ((isBuyDirection && profitPrice.compareTo(costPrice) < 0 && isPriceDifferenceExceedsThreshold) ||
                    (!isBuyDirection && costPrice.compareTo(profitPrice) > 0 && isPriceDifferenceExceedsThreshold)) {
                log.error("Price data inconsistency detected for order: {}, profitPrice: {}, costPrice: {}, isBuyDirection: {}",
                        combinedOrderData.getOrderId(), profitPrice, costPrice, isBuyDirection);
                String errorMessage = String.format("价格数据不一致, 利润价: %s, 成本价: %s, 是否买入: %s", profitPrice, costPrice, isBuyDirection);
                alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                        ReconOrderTypeEnum.CONVERT,
                        combinedOrderData.getOrderId(),
                        errorMessage);
                throw new ReconException(ReconOrderFailureEnum.INCONSISTENT_PRICE_DATA, "价格数据不一致，订单ID: " + combinedOrderData.getOrderId());
            }
        } else {
            // 非USDT交易对，直接比较利润价格和成本价格
            if (profitPrice.compareTo(costPrice) < 0 && isPriceDifferenceExceedsThreshold) {
                log.error("Price data inconsistency detected for order: {}, profitPrice: {}, costPrice: {}",
                        combinedOrderData.getOrderId(), profitPrice, costPrice);
                String errorMessage = String.format("价格数据不一致, 利润价: %s, 成本价: %s", profitPrice, costPrice);
                alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                        ReconOrderTypeEnum.CONVERT,
                        combinedOrderData.getOrderId(),
                        errorMessage);
                throw new ReconException(ReconOrderFailureEnum.INCONSISTENT_PRICE_DATA, "价格数据不一致，订单ID: " + combinedOrderData.getOrderId());
            }
        }
    }

    /**
     * 验证行情价格
     * @param combinedOrderData 组合订单数据
     */
    private void validQuotaPrice(CombinedOrderData combinedOrderData) {

        if (reconOrderConfig.isEnableQuotaPrice()) {
            // 如果pt2和pc2都为null，说明是USDT交易对
            boolean isUsdt = Objects.isNull(combinedOrderData.getMainOrder().getPt2()) &&
                    Objects.isNull(combinedOrderData.getMainOrder().getPc2());

            Integer coinId = getCoinId(combinedOrderData, isUsdt);
            String coinName = commonService.getCoinName(coinId);
            long time = combinedOrderData.getMainOrder().getCreateTime().getTime();
            KLinePeriodDto spotKline1m = klineInnerService.getSpotKline1m(coinName, time);
            if (spotKline1m == null) {
                String errorMessage = String.format("获取行情数据失败, coinName: %s, time: %s", coinName, time);
                alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                        ReconOrderTypeEnum.CONVERT,
                        combinedOrderData.getOrderId(),
                        errorMessage);
                log.warn("No KLine data found for coin: {} at time: {}", coinName, time);
                return;
            }
            // 获取最高价和最低价
            BigDecimal highPrice = spotKline1m.getHigh();
            BigDecimal lowPrice = spotKline1m.getLow();
            // 判断利润价格是否在K线的最高价和最低价范围内，如果不在，判断是否超出范围的百分比是否在阈值以内
            BigDecimal profitPrice = isUsdt ? combinedOrderData.getMainOrder().getPt1() : combinedOrderData.getMainOrder().getPt2();
            if (profitPrice.compareTo(highPrice) > 0 || profitPrice.compareTo(lowPrice) < 0) {
                // 如果利润价格超出K线范围，判断超出范围的百分比是否在阈值以内
                String lowThresholdRate = reconOrderConfig.getLowThresholdRate();
                String highThresholdRate = reconOrderConfig.getHighThresholdRate();
                BigDecimal highThreshold = highPrice.multiply(new BigDecimal(lowThresholdRate));
                BigDecimal lowThreshold = lowPrice.multiply(new BigDecimal(highThresholdRate));
                if (profitPrice.subtract(highPrice).abs().compareTo(highThreshold) > 0 &&
                        profitPrice.subtract(lowPrice).abs().compareTo(lowThreshold) > 0) {
                    // 排除异常记录订单超出阈值是多少
                    log.warn("Profit price is out of KLine range for order: {}, profitPrice: {}, highPrice: {}, lowPrice: {}",
                            combinedOrderData.getOrderId(), profitPrice, highPrice, lowPrice);
                    String errorMessage = String.format("行情价格超出阈值范围. 利润价: %s, K线最高价: %s, K线最低价: %s, 高价阈值: %s, 低价阈值: %s ,最高阈值: %s, 最低阈值: %s",
                            profitPrice, highPrice, lowPrice, highThreshold, lowThreshold,highThresholdRate, lowThresholdRate);
                    alarmNotifyService.alarm(CHECK_CONVERT_RECON_ORDER_ERROR,
                            ReconOrderTypeEnum.CONVERT,
                            combinedOrderData.getOrderId(),
                            errorMessage);
                    throw new ReconException(ReconOrderFailureEnum.INCONSISTENT_QUOTA_PRICE, "行情价格不一致，订单ID: " + combinedOrderData.getOrderId());
                }
            }
        }

    }

    /**
     * 获取币种ID
     * @param combinedOrderData 组合订单数据
     * @param isUsdt 是否是USDT交易对
     * @return 币种ID
     */
    private static Integer getCoinId(CombinedOrderData combinedOrderData, boolean isUsdt) {
        Integer coinId;
        // 如果usdt交易对
        if (isUsdt) {
            boolean isBuyDirection = combinedOrderData.getMainOrder().getFromCoinId().equals(CoinIdEnum.USDT.getCode());
            if (isBuyDirection) {
                coinId = combinedOrderData.getMainOrder().getToCoinId();
            } else {
                coinId = combinedOrderData.getMainOrder().getFromCoinId();
            }
        } else {
            // 非USDT交易对
            coinId = combinedOrderData.getMainOrder().getToCoinId();
        }
        return coinId;
    }

}
