package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.ConvertOrder;
import com.upex.reconciliation.service.business.convert.OrderDataMatcher;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: kafka消费者消费消息, 手动同步提交offset
 **/

@Slf4j
public class ConvertOrderConsumerRunnable implements KafkaConsumerLifecycle {
    private List<KafkaConsumer<String, Message>> consumerList;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private String kafkaConsumerKey;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private KafkaConsumer consumer;
    private OrderDataMatcher orderDataMatcher;
    private ReconOrderConfig reconOrderConfig;

    public ConvertOrderConsumerRunnable(KafkaConsumerConfig kafkaConsumerConfig, OrderDataMatcher orderDataMatcher) {
        this.topic = "recon_consumer_convert_type_10";
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.partitionNum = 3;
        this.orderDataMatcher = orderDataMatcher;
        this.consumerList = new ArrayList<>();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "dev-test-v351-kafka01.bitget.tools:9092,dev-test-v351-kafka02.bitget.tools:9092,dev-test-v351-kafka03.bitget.tools:9092");
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, "kafka-group-tayson");
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 300);
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    @Override
    public void run() {
        // todo 读配置消费
        // 初始化
        log.info("AccountProfitAssetsConsumerRunnable consumerRunnables.run");
        {
            consumer = new KafkaConsumer<String, Message>(consumerConfig);
            consumer.subscribe(Arrays.asList(topic));
            log.info("SyncCenterUserConsumerRunnable run 。。。");
            GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
            while (running) {
                try {
                    // 从kafka集群中拉取消息df
                    ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                    consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                        @Override
                        public void accept(ConsumerRecord<String, Message> consumerRecord) {
                            List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                            handle(flatMessages);
                            log.info(flatMessages.toString());
                        }
                    });
                    consumer.commitSync();
                } catch (Exception e) {
                    log.error("SyncCenterUserConsumerRunnable startConsume error ", e);
                }
            }
            consumer.close();
            closeConsumerPatition.add(0);
            log.info("SyncCenterUserConsumerRunnable consumer.close success {} {}");
        }
    }


    public Integer handle(List<FlatMessage> flatMessages) {
        // todo 根据apollo配置处理不同业务类型的闪兑消息 accountType '1-普通用户，其他-系统账户'
//        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        Integer bizMessageSize = 0;
        for (FlatMessage flatMessage : flatMessages) {
            List<ConvertOrder> convertOrders = buildConvertOrderList(flatMessage);
            for (ConvertOrder convertOrder : convertOrders) {
                if (convertOrder.getAccountType().equals(1)) {
                    // 处理普通用户的闪兑订单
                    orderDataMatcher.saveMainOrderData(convertOrder);
                } else {
                    // todo 处理系统账户的闪兑订单
                    log.info("Processing system account convert order: {}", JSON.toJSONString(convertOrder));
                }

            }
        }
        return bizMessageSize;
    }

    /**
     * 消息解析
     *
     * @param flatMessage
     * @return
     */
    private List<ConvertOrder> buildConvertOrderList(FlatMessage flatMessage) {
        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("ConvertOrderConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
                log.error("ConvertOrderConsumerRunnable.buildConvertOrderList DELETE data:{}", JSON.toJSONString(dataList));
                return Collections.emptyList();
            case UPDATE:
            case INSERT:
                log.info("ConvertOrderConsumerRunnable.buildConvertOrderList INSERT data:{}", JSON.toJSONString(dataList));
                return messageDecode(dataList);
        }
        return Collections.emptyList();
    }

    /**
     * 解码消息为ConvertOrder对象
     *
     * @param dataList
     * @return
     */
    private List<ConvertOrder> messageDecode(List<Map<String, String>> dataList) {
        List<ConvertOrder> convertOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                ma
                List<String> filterStatus = reconOrderConfig.getOrderFilterStatus();
                if (!filterStatus.contains(map.get("status"))) {
                    continue;
                }
                ConvertOrder ConvertOrder = new ConvertOrder();
                // 设置ConvertOrder的属性
                ConvertOrder convertOrder = new ConvertOrder();
                convertOrder.setOrderId(map.get("order_id") == null ? null : Long.valueOf(map.get("order_id")));
                convertOrder.setClientOrderId(map.get("client_order_id") == null ? null : Long.valueOf(map.get("client_order_id")));
                convertOrder.setAccountId(map.get("account_id") == null ? null : Long.valueOf(map.get("account_id")));
                convertOrder.setAccountType(map.get("account_type") == null ? null : Integer.valueOf(map.get("account_type")));
                convertOrder.setFromCoinId(map.get("from_coin_id") == null ? null : Integer.valueOf(map.get("from_coin_id")));
                convertOrder.setFromCoinCount(map.get("from_coin_count") == null ? null : new BigDecimal(map.get("from_coin_count")));
                convertOrder.setToCoinId(map.get("to_coin_id") == null ? null : Integer.valueOf(map.get("to_coin_id")));
                convertOrder.setToCoinCount(map.get("to_coin_count") == null ? null : new BigDecimal(map.get("to_coin_count")));
                convertOrder.setPc1(map.get("pc1") == null ? null : new BigDecimal(map.get("pc1")));
                convertOrder.setPt1(map.get("pt1") == null ? null : new BigDecimal(map.get("pt1")));
                convertOrder.setPc2(map.get("pc2") == null ? null : new BigDecimal(map.get("pc2")));
                convertOrder.setPt2(map.get("pt2") == null ? null : new BigDecimal(map.get("pt2")));
                convertOrder.setBaseSpread1(map.get("base_spread1") == null ? null : new BigDecimal(map.get("base_spread1")));
                convertOrder.setFloatSpread1(map.get("float_spread1") == null ? null : new BigDecimal(map.get("float_spread1")));
                convertOrder.setBaseSpread2(map.get("base_spread2") == null ? null : new BigDecimal(map.get("base_spread2")));
                convertOrder.setFloatSpread2(map.get("float_spread2") == null ? null : new BigDecimal(map.get("float_spread2")));
                convertOrder.setConvertFeeRate(map.get("convert_fee_rate") == null ? null : new BigDecimal(map.get("convert_fee_rate")));
                convertOrder.setConvertFeeRate2(map.get("convert_fee_rate2") == null ? null : new BigDecimal(map.get("convert_fee_rate2")));
                convertOrder.setQuotedType(map.get("quoted_type") == null ? null : Integer.valueOf(map.get("quoted_type")));
                convertOrder.setShowFlag(map.get("show_flag") == null ? null : Integer.valueOf(map.get("show_flag")));
                convertOrder.setEps(map.get("eps") == null ? null : Integer.valueOf(map.get("eps")));
                convertOrder.setStatus(map.get("status") == null ? null : Integer.valueOf(map.get("status")));
                convertOrder.setParams(map.get("params"));
                convertOrder.setCreateTime(map.get("create_time") == null ? null : DateUtil.getMillisecondDate((map.get("create_time"))));
                convertOrder.setUpdateTime(map.get("update_time") == null ? null : DateUtil.getMillisecondDate((map.get("update_time"))));
                convertOrder.setVersion(map.get("version") == null ? null : Long.valueOf(map.get("version")));
                convertOrders.add(ConvertOrder);
            }
        }
        return convertOrders;
    }



    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-profit-";
    }
}


