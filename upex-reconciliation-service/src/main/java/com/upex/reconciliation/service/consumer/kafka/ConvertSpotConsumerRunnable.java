package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.OrderDataMatcher;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;

import static com.upex.reconciliation.service.utils.MetricsUtil.*;

/**
 * 闪兑现货流水处理
 **/

@Slf4j
public class ConvertSpotConsumerRunnable implements KafkaConsumerLifecycle {
    private Byte accountType;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private KafkaConsumer consumer;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private AlarmNotifyService alarmNotifyService;
    private OrderDataMatcher orderDataMatcher;
    private ReconOrderConfig reconOrderConfig;

    public ConvertSpotConsumerRunnable(String kafkaServers, KafkaConsumerConfig kafkaConsumerConfig,
                                       AccountAssetsServiceFactory accountAssetsServiceFactory,
                                       AlarmNotifyService alarmNotifyService,OrderDataMatcher orderDataMatcher) {

        this.accountAssetsServiceFactory = accountAssetsServiceFactory;
        this.orderDataMatcher = orderDataMatcher;
        this.alarmNotifyService = alarmNotifyService;
        this.accountType = AccountTypeEnum.SPOT.getCode();
        this.topic = kafkaConsumerConfig.getTopicName();
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    @Override
    public void run() {
        // 初始化
        log.info("AccountProfitAssetsConsumerRunnable consumerRunnables.run");
        {
            consumer = new KafkaConsumer<String, Message>(consumerConfig);
            consumer.subscribe(Arrays.asList(topic));
            // 设置从最早的偏移量开始消费
                // 设置从最新的偏移量开始消费
            while (running) {
                try {
                    // 从kafka集群中拉取消息df
                    ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                    consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                        @Override
                        public void accept(ConsumerRecord<String, Message> consumerRecord) {
                            List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                            Long consumerTimestamp = System.currentTimeMillis();
                            Integer bizMessageSize = handle(accountType, flatMessages, consumerRecord.offset(), consumerRecord.partition(), consumerRecord.timestamp(), consumerTimestamp);

                        }
                    });
                    consumer.commitSync();
                } catch (Exception e) {
                    log.error("ConvertSpotConsumerRunnable startConsume error ", e);
                }
            }
            consumer.close();
            closeConsumerPatition.add(0);
            log.info("ConvertSpotConsumerRunnable consumer.close success {} {}");
        }
    }


    /**
     * 消息处理
     *
     * @param accountType
     * @param flatMessages
     * @param offset
     * @param partition
     * @param kafkaTimestamp
     * @param consumerTimestamp
     * @return
     */
    public Integer handle(Byte accountType, List<FlatMessage> flatMessages, Long offset, Integer partition, Long kafkaTimestamp, Long consumerTimestamp) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        Integer bizMessageSize = 0;
        for (FlatMessage flatMessage : flatMessages) {
            List<CommonBillChangeData> commonBillChangeDataList = buildBillChangeDataList(accountType, apolloBizConfig, flatMessage, offset, partition);
            for (CommonBillChangeData commonBillChangeData : commonBillChangeDataList) {
                String bizType = commonBillChangeData.getBizType();
                if (reconOrderConfig.getFlowTypes().contains(bizType)) {
                    // 转换为ReconOrderData
                    ConvertBill bill = convertToReconOrderData(commonBillChangeData);
                    orderDataMatcher.saveFlowOrderData(bill,bizType);
                    log.info("ConvertSpotConsumerRunnable handle commonBillChangeData bizType {},bizId：{}",bizType,commonBillChangeData.getOrderId());
                }
            }
        }
        return bizMessageSize;
    }

    /**
     * 将CommonBillChangeData转换为ReconOrderData
     */
    private ConvertBill convertToReconOrderData(CommonBillChangeData commonBillChangeData) {
        ConvertBill bill = new ConvertBill();

        // 设置订单ID
        if (commonBillChangeData.getOrderId() != null) {
            bill.setOrderId(Long.valueOf(commonBillChangeData.getOrderId()));
        }
        bill.setBillType(commonBillChangeData.getBizType());
        // 设置熟悉
        bill.setAccountId(commonBillChangeData.getAccountId());
        bill.setCoinId(commonBillChangeData.getCoinId());
        bill.setBalanceChange(commonBillChangeData.getProp1());
        return bill;
    }




    /**
     * 消息解析
     *
     * @param accountType
     * @param apolloBizConfig
     * @param flatMessage
     * @param offset
     * @param partition
     * @return
     */
    private List<CommonBillChangeData> buildBillChangeDataList(Byte accountType, ApolloReconciliationBizConfig apolloBizConfig, FlatMessage flatMessage, Long offset, Integer partition) {
        MetricsUtil.counter(COUNTER_BINLOG_CMD_ALL);
        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("AccountProfitAssetsConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
                // log.error("AccountProfitAssetsConsumerRunnable.buildBillChangeDataList UPDATE accountType:{} newData:{} oldData:{}", accountType, JSON.toJSONString(dataList));
                return Collections.emptyList();
            case UPDATE:
                return Collections.emptyList();
            case INSERT:
                MetricsUtil.counter(COUNTER_BINLOG_CMD_INSERT);
                if (apolloBizConfig.getReconKafkaOpsConfig().isKafkaConsumeSkipBusinessLogic()) {
                    List<String> createTimes = new ArrayList<String>();
                    List<String> bizTimes = new ArrayList<String>();
                    List<String> createDates = new ArrayList<String>();
                    List<String> bizIds = new ArrayList<String>();
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        for (Map<String, String> dataMap : dataList) {
                            if (dataMap.get("create_time") != null) {
                                createTimes.add(dataMap.get("create_time"));
                            }
                            if (dataMap.get("biz_time") != null) {
                                bizTimes.add(dataMap.get("biz_time"));
                            }
                            if (dataMap.get("create_date") != null) {
                                createDates.add(dataMap.get("create_date"));
                            }
                            if (dataMap.get("bill_id") != null) {
                                bizIds.add((dataMap.get("bill_id")));
                            } else if (dataMap.get("id") != null) {
                                bizIds.add((dataMap.get("id")));
                            }
                        }
                    }
                    BizLogUtils.log(LogLevelEnum.FULL, apolloBizConfig, "ConvertSpotConsumerRunnable KafkaMsgDecoder accountType {} partition:{}, offset:{}  bizIds:{},createTimes:{}, bizTimes:{} ,createDates:{} ", accountType, partition, offset, JSONObject.toJSONString(bizIds), JSONObject.toJSONString(createTimes), JSONObject.toJSONString(bizTimes), JSONObject.toJSONString(createDates));
                    return Collections.emptyList();
                }
                List<CommonBillChangeData> list = accountAssetsServiceFactory.getMessageDecoder(AccountTypeEnum.SPOT.getCode()).messageDecode(alarmNotifyService, dataList, flatMessage, partition, offset, accountType);
//                BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, apolloBizConfig, "ConvertSpotConsumerRunnable KafkaMsgDecoder accountType {} partition:{}, offset:{} changelist:{} ", accountType, partition, offset, JSONObject.toJSONString(list));
                return list;
        }
        return Collections.emptyList();
    }


    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-profit-convert-spot";
    }
}


